<?php

namespace App\Filament\Resources;

use Filament\Resources\Resource;

abstract class BaseResource extends Resource
{
    public static function getPluralModelLabel(): string
    {
        return static::getModelLabel();
    }
    public static function getModelLabel(): string
    {
        $name = str(class_basename(static::getModel()))->snake()->toString();
        return __('tables.'.$name);
    }
}
