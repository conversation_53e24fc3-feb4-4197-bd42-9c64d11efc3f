<?php

namespace App\Filament\Resources;

use App\Enums\CustomerSourceEnum;
use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use App\Models\Address;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerResource extends BaseResource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '客户管理';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('客户名称')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('source')
                    ->required()
                    ->options(CustomerSourceEnum::class),
                Forms\Components\TextInput::make('telephone')
                    ->tel()
                    ->maxLength(50),
                Forms\Components\TextInput::make('website')
                    ->maxLength(255),
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Select::make('province')
                            ->options(Address::getProvinces())
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                                $set('city', null);
                                $set('district', null);
                                self::updateFullAddress($set, $get);
                            })
                            ->columnSpan(3),
                        Forms\Components\Select::make('city')
                            ->options(function (Forms\Get $get) {
                                $provinceName = $get('province');

                                if (!$provinceName) {
                                    return [];
                                }

                                return Address::getChildrenByParentCode($provinceName)
                                    ->where('level', 2)
                                    ->pluck('name', 'name')
                                    ->toArray();
                            })
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                                $set('district', null);
                                self::updateFullAddress($set, $get);
                            })
                            ->disabled(fn (Forms\Get $get) => empty($get('province')))
                            ->columnSpan(3),
                        Forms\Components\Select::make('district')
                            ->options(function (Forms\Get $get) {
                                $cityName = $get('city');

                                if (!$cityName) {
                                    return [];
                                }

                                return Address::getChildrenByParentCode($cityName)
                                    ->where('level', 3)
                                    ->pluck('name', 'name')
                                    ->toArray();
                            })
                            ->live()
                            ->afterStateUpdated(fn (Forms\Set $set, Forms\Get $get) => self::updateFullAddress($set, $get))
                            ->disabled(fn (Forms\Get $get) => empty($get('city')))
                            ->columnSpan(3),
                        Forms\Components\TextInput::make('address')
                            ->live(true)
                            ->afterStateUpdated(fn (Forms\Set $set, Forms\Get $get) => self::updateFullAddress($set, $get))
                            ->maxLength(255)
                            ->columnSpan(3),
                    ])
                    ->columns(12)
                    ->columnSpan('full'),
                Forms\Components\TextInput::make('full_address')
                    ->maxLength(255)
                    ->readOnly(),
                Forms\Components\TextInput::make('assigned_to_user_id')
                    ->hidden()
                    ->numeric(),
            ]);
    }

    /**
     * 更新完整地址字段
     */
    public static function updateFullAddress(Forms\Set $set, Forms\Get $get): void
    {
        $provinceName = $get('province');
        $cityName = $get('city');
        $districtName = $get('district');
        $address = $get('address');

        // 获取省市区的名称
        $provinceName = $provinceName ?: '';
        $cityName = $cityName ?: '';
        $districtName = $districtName ?: '';

        // 拼接完整地址
        $fullAddress = implode('', array_filter([$provinceName, $cityName, $districtName, $address]));

        $set('full_address', $fullAddress);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source')
                    ->searchable(),
                Tables\Columns\TextColumn::make('telephone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('website')
                    ->searchable(),
                Tables\Columns\TextColumn::make('province')
                    ->label('省')
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->label('市')
                    ->searchable(),
                Tables\Columns\TextColumn::make('district')
                    ->label('区')
                    ->searchable(),
                Tables\Columns\TextColumn::make('address')
                    ->searchable(),
//                Tables\Columns\TextColumn::make('full_address')
//                    ->searchable(),
                Tables\Columns\TextColumn::make('assigned_to_user_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ContactsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
