<?php

namespace App\Filament\Resources;

use App\Exports\QuotationEnExport;
use App\Filament\Resources\QuotationResource\Pages;
use App\Filament\Resources\QuotationResource\RelationManagers;
use App\Models\Quotation;
use App\Models\Product;
use App\Utils\CommonUtil;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Icetalker\FilamentTableRepeater\Forms\Components\TableRepeater;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Group;
use App\Enums\QuotationStatusEnum;
use pxlrbt\FilamentExcel\Actions\Tables\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use App\Exports\QuotationExport;

class QuotationResource extends BaseResource
{
    protected static ?string $model = Quotation::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '销售管理';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('quotation_number')
                    ->required()
                    ->default(CommonUtil::generateOrderNumber('QO'))
                    ->maxLength(50),
                Group::make([
                    Select::make('customer_id')
                        ->relationship('customer', 'name')
                        ->searchable()
                        ->required()
                        ->preload()
                        ->live()
                        ->afterStateUpdated(function ($state, Forms\Set $set) {
                            // 清空联系人选择
                            $set('contact_id', null);

                            // 获取选中的客户完整地址并设置到shipping_address字段
                            $shipping_address = '';
                            if ($state) {
                                $customer = \App\Models\Customer::find($state);
                                $shipping_address = $customer?->full_address ?: '';
                            }
                            $set('shipping_address', $shipping_address);
                        }),
                    Select::make('contact_id')
                        ->relationship(
                            name: 'contact',
                            titleAttribute: 'name',
                            modifyQueryUsing: fn (Builder $query, Forms\Get $get) =>  $query->where('customer_id', $get('customer_id'))
                        )
                        ->live()
                        ->preload()
                        ->afterStateUpdated(function ($state, Forms\Set $set) {
                            if ($state) {
                                $contact = \App\Models\Contact::find($state);
                                if ($contact) {
                                    $set('recipient_name', $contact->name);
                                    $set('recipient_phone', $contact->mobile_phone ?? '');
                                }
                            }
                        }),
                ])->columns(2),
                DatePicker::make('quotation_date')
                    ->default(date('Y-m-d'))
                    ->maxDate(now())
                    ->required(),
                DatePicker::make('expiry_date'),
//                Group::make([
//                    TextInput::make('recipient_name')
//                        ->maxLength(100),
//                    TextInput::make('recipient_phone')
//                        ->maxLength(20),
//                    TextInput::make('shipping_address')
//                        ->maxLength(255)->columnSpan(2),
//                ])->columns(4)
//                    ->columnSpan('full'),
                TableRepeater::make('items')
                    ->label('报价详情')
                    ->relationship()
                    ->reorderable()
                    ->colStyles([
                        'unit' => 'width: 50px;',
                        'quantity' => 'width: 100px;',
                        'unit_price' => 'width: 100px;',
                        'total_line_amount' => 'width: 100px;',
                    ])
                    ->schema([
                        Select::make('product_id')
                            ->relationship('product', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('unit_price', $product->selling_price ?? 0);
                                        $set('unit', $product->unit ?? '');
                                        $set('name', $product->name ?? '');
                                        $set('spec', $product->spec ?? '');

                                        // 设置默认数量并触发计算
                                        $set('quantity', 1);

                                        // 手动触发计算逻辑，因为$set()不会自动触发afterStateUpdated
                                        $quantity = 1;
                                        $unitPrice = $product->selling_price ?? 0;
                                        $lineAmount = bcmul($quantity, $unitPrice);
                                        $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                        // 更新总金额
                                        self::updateTotalAmount($get, $set, true);
                                    }
                                }
                            })
                            ->columnSpan(1),
                        TextInput::make('name')
                            ->maxLength(100)
                            ->columnSpan(1),
                        TextInput::make('spec')
                            ->maxLength(100)
                            ->columnSpan(2),
                        TextInput::make('unit')
                            ->maxLength(20)
                            ->readOnly()
                            ->columnSpan(1),
                        TextInput::make('quantity')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->live(true)
                            ->columnSpan(1)
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // 当数量更新时，重新计算行总额
                                $quantity = $get('quantity') ?? 0;
                                $unitPrice = $get('unit_price') ?? 0;
                                $lineAmount = bcmul($quantity, $unitPrice);
                                $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                // 更新总金额
                                self::updateTotalAmount($get, $set, true);
                            }),
                        TextInput::make('unit_price')
                            ->required()
                            ->numeric()
                            ->default(0.00)
                            ->live()
                            ->columnSpan(1)
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // 当单价更新时，重新计算行总额
                                $quantity = $get('quantity') ?? 0;
                                $unitPrice = $get('unit_price') ?? 0;
                                $lineAmount = bcmul($quantity, $unitPrice);
                                $set('unit_price', $unitPrice);
                                $set('total_line_amount', CommonUtil::formatToAmount($lineAmount));

                                // 更新总金额
                                self::updateTotalAmount($get, $set, true);
                            }),
                        TextInput::make('total_line_amount')
                            ->required()
                            ->numeric()
                            ->default(0.00)
                            ->readOnly()
                            ->live()
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                self::updateTotalAmount($get, $set);
                            })
                            ->columnSpan(1),
                        TextInput::make('remark')
                            ->maxLength(255)
                            ->columnSpan(2),
                    ]),
                TextInput::make('original_amount')
                    ->required()
                    ->numeric()
                    ->default(0.00)
                    ->readOnly(),
                TextInput::make('discount_amount')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->default(0.00)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                        self::updateTotalAmount($get, $set);
                    }),
                TextInput::make('total_amount')
                    ->required()
                    ->numeric()
                    ->default(0.00)
                    ->live(onBlur: true)
                    ->readOnly()
                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                        $str = CommonUtil::convertAmountToChinese($state);
                        $set('total_amount_in_words', $str);
                    }),
                TextInput::make('total_amount_in_words')
                    ->required()
                    ->maxLength(255)
                    ->readOnly(),
                Select::make('status')
                    ->required()
                    ->options(QuotationStatusEnum::class)
                    ->default(QuotationStatusEnum::DRAFT),
                TextInput::make('salesperson_id')
                    ->numeric()
                    ->default(0)
                    ->hidden(),
                Forms\Components\Textarea::make('remark')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('quotation_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->numeric()
                    ->sortable(),
//                Tables\Columns\TextColumn::make('contact.name')
//                    ->numeric()
//                    ->sortable(),
                Tables\Columns\TextColumn::make('quotation_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('expiry_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('recipient_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('recipient_phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipping_address')
                    ->searchable(),
                Tables\Columns\TextColumn::make('original_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('discount_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount_in_words')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'sent' => 'blue',
                        'accepted' => 'success',
                        'rejected' => 'danger',
                        'expired' => 'warning',
                        'converted' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('salesperson_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('export')
                    ->label('导出')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function (Quotation $record) {
                        $export = new QuotationExport($record->id);
                        return \Maatwebsite\Excel\Facades\Excel::download($export, 'quotation-'.$record->quotation_number.'.xlsx');
                    }),
                Tables\Actions\Action::make('export-en')
                    ->label('导出英文')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function (Quotation $record) {
                        $export = new QuotationEnExport($record->id);
                        return \Maatwebsite\Excel\Facades\Excel::download($export, 'quotation-'.$record->quotation_number.'-en.xlsx');
                    }),
            ],Tables\Enums\ActionsPosition::BeforeColumns)
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuotations::route('/'),
            'create' => Pages\CreateQuotation::route('/create'),
            'edit' => Pages\EditQuotation::route('/{record}/edit'),
        ];
    }

    /**
     * 更新报价总金额
     *
     * @param Forms\Get $get
     * @param Forms\Set $set
     * @return void
     */
    public static function updateTotalAmount(Forms\Get $get, Forms\Set $set, $isInSubtable = false): void
    {
        $prefix = $isInSubtable ? '../../' : '';
        $items = $get($prefix . 'items');
        if (empty($items)) {
            return;
        }

        $originalAmount = 0;
        $totalAmount = 0;

        foreach ($items as $item) {
            $quantity = $item['quantity'] ?? 0;
            $unitPrice = $item['unit_price'] ?? 0;

            // 计算行总额
            $lineTotal = $quantity * $unitPrice;

            $originalAmount += $lineTotal;
            $totalAmount += $lineTotal;
        }
        $discountAmount = $get($prefix.'discount_amount') ?? 0;
        // 计算折扣金额
        $totalAmount = bcsub($originalAmount, $discountAmount);
        // 设置总金额字段，保留两位小数
        $set($prefix . 'original_amount', CommonUtil::formatToAmount($originalAmount));
        $set($prefix . 'discount_amount', $discountAmount);
        $set($prefix . 'total_amount', CommonUtil::formatToAmount($totalAmount));

        // 更新中文大写金额
        $str = CommonUtil::convertAmountToChinese($totalAmount);
        $set($prefix . 'total_amount_in_words', $str);
    }
}
