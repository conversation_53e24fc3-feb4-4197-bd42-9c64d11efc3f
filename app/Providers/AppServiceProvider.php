<?php

namespace App\Providers;

use Filament\Forms\Components\Field;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Columns\Column;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 解除所有模型的保护
        Model::unguard();
        Field::configureUsing(function (Field $component): void {
            // 默认开启翻译
            $component->translateLabel();
            $label = $component->getName();
            $component->label('attributes.'.$label);
        });
        Column::configureUsing(function (Column $component): void {
            // 默认开启翻译
            $component->translateLabel();
            $label = $component->getName();
            $component->label('attributes.'.$label);
        });
        Action::configureUsing(function (Action $component): void {
            // 默认开启翻译
            $component->translateLabel();
            $label = $component->getName();
            $component->label('attributes.'.$label);
        });
        Table::$defaultDateDisplayFormat = 'Y-m-d';
        Table::$defaultDateTimeDisplayFormat = 'Y-m-d H:i:s';
//        TextColumn::configureUsing(function (TextColumn $column): void {
//
//            if ($column->isDate()) {
//                $column->date('Y-m-d');
//            } elseif ($column->isDateTime()) {
//                $column->dateTime('Y-m-d H:i:s');
//            }
//        });
    }
}
