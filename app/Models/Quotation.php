<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\QuotationStatusEnum;

/**
 * @property int $id 主键ID
 * @property string $quotation_number 报价单号
 * @property int $customer_id 客户ID
 * @property int|null $contact_id 联系人ID
 * @property string $quotation_date 报价日期
 * @property string|null $expiry_date 报价有效期
 * @property string|null $shipping_address 送货地址
 * @property string|null $recipient_name 收件人姓名
 * @property string|null $recipient_phone 收件人手机号
 * @property string $original_amount 报价原价总额
 * @property string $discount_amount 优惠金额
 * @property string $total_amount 报价金额
 * @property string $total_amount_in_words 报价金额大写
 * @property QuotationStatusEnum $status 状态
 * @property int $salesperson_id 销售员ID
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\Contact|null $contact
 * @property-read \App\Models\Customer|null $customer
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\QuotationItem> $items
 * @property-read int|null $items_count
 */
class Quotation extends Model
{
    /**
     * 获取报价单所属的客户。
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * 获取报价单的联系人。
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * 获取报价单的所有报价项。
     */
    public function items(): HasMany
    {
        return $this->hasMany(QuotationItem::class, 'quotation_id');
    }
}