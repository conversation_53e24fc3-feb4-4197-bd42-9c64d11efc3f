<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $quotation_id 报价主表ID
 * @property int $product_id 产品ID
 * @property string $name 产品名字
 * @property string $spec 规格
 * @property int $quantity 数量
 * @property string $unit_price 单价
 * @property string $discount_rate 折扣率 (%)
 * @property string $tax_rate 税率 (%)
 * @property string $total_line_amount 行总价
 * @property string|null $unit 单位
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\Quotation|null $quotation
 */
class QuotationItem extends Model
{
    /**
     * 获取报价项所属的报价单。
     */
    public function quotation(): BelongsTo
    {
        return $this->belongsTo(Quotation::class, 'quotation_id');
    }

    /**
     * 获取报价项对应的产品。
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}