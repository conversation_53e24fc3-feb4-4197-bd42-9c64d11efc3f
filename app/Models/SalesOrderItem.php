<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $sales_order_id 销售订单主表ID
 * @property int $product_id 销售产品ID
 * @property int $quantity 销售数量
 * @property string $unit_price 单价
 * @property string $discount_rate 折扣率 (%)
 * @property string $tax_rate 税率 (%)
 * @property string $total_line_amount 行总价
 * @property string $shipped_quantity 已发货数量
 * @property string|null $unit 单位
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\SalesOrder|null $salesOrder
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereDiscountRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereSalesOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereShippedQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereTaxRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereTotalLineAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrderItem whereUpdaterId($value)
 * @mixin \Eloquent
 */
class SalesOrderItem extends Model
{
    /**
     * 获取订单项所属的销售订单。
     */
    public function salesOrder(): BelongsTo
    {
        return $this->belongsTo(SalesOrder::class, 'sales_order_id');
    }

    /**
     * 获取订单项对应的产品。
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
