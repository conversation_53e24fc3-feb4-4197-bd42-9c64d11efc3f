<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property int $customer_id 所属客户ID
 * @property string $name 联系人姓名
 * @property string|null $title 职位
 * @property string|null $department 部门
 * @property string|null $email 邮箱
 * @property string|null $mobile_phone 手机
 * @property string|null $telephone 办公电话
 * @property int $is_primary 是否主要联系人
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\Customer|null $customer
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIsPrimary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereMobilePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereTelephone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereUpdaterId($value)
 * @mixin \Eloquent
 */
class Contact extends Model
{
    /**
     * 获取联系人所属的客户。
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
