<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $id
 * @property string $parent_name 父级名称
 * @property string $name 名称
 * @property int $level 级别 1省 2市 3区
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereParentName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereId($value)
 * @mixin \Eloquent
 */
class Address extends Model
{

    /**
     * 获取省份列表
     */
    public static function getProvinces()
    {
        // 根据新的数据结构修改实现
        return self::where('level', 1)
            ->pluck('name', 'name')
            ->toArray();
    }

    /**
     * 根据父级名称获取子级地区
     */
    public static function getChildrenByParentCode($parentName)
    {
        return self::where('parent_name', $parentName)
            ->get();
    }
}