<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property int $id
 * @property int $product_category_id 产品分类ID
 * @property string|null $image_url 产品图片
 * @property string $name 产品名称
 * @property string|null $spec 规格
 * @property string|null $desc 产品描述
 * @property string $unit 单位 (如: 个, 件, kg)
 * @property numeric $purchase_price 采购单价
 * @property numeric $selling_price 销售单价
 * @property numeric $overseas_selling_price 境外销售单价
 * @property string|null $size 规格尺寸 如:1*2*3cm
 * @property numeric $weight 重量(g)
 * @property numeric $volume 体积(cm³)
 * @property string|null $package_info 包装规格 如:100双/箱
 * @property string|null $package_size 包装尺寸 如:1*2*3cm
 * @property numeric $package_volume 包装体积(cm³)
 * @property numeric $package_weight 包装重量(kg)
 * @property string|null $overseas_package_info 境外包装规格 如:100双/箱
 * @property string|null $overseas_package_size 境外包装尺寸 如:1*2*3cm
 * @property numeric $overseas_package_volume 境外包装体积(cm³)
 * @property numeric $overseas_package_weight 境外包装重量(kg)
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\ProductCategory|null $category
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PurchaseOrderItem> $purchaseOrderItems
 * @property-read int|null $purchase_order_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SalesOrderItem> $salesOrderItems
 * @property-read int|null $sales_order_items_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOverseasPackageInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOverseasPackageSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOverseasPackageVolume($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOverseasPackageWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOverseasSellingPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePackageInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePackageSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePackageVolume($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePackageWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereProductCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePurchasePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSellingPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSpec($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUpdaterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereVolume($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWeight($value)
 * @mixin \Eloquent
 */
class Product extends Model  implements HasMedia
{
    use InteractsWithMedia;
    /**
     * 可批量赋值的属性。
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_category_id',
        'image_url',
        'name',
        'spec',
        'desc',
        'unit',
        'purchase_price',
        'selling_price',
        'overseas_selling_price',
        'size',
        'volume',
        'weight',
        'package_info',
        'package_size',
        'package_volume',
        'package_weight',
        'overseas_package_info',
        'overseas_package_size',
        'overseas_package_volume',
        'overseas_package_weight',
    ];

    /**
     * 类型转换。
     *
     * @var array<string, string>
     */
    protected $casts = [
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'overseas_selling_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'volume' => 'decimal:2',
        'package_weight' => 'decimal:2',
        'package_volume' => 'decimal:2',
        'overseas_package_weight' => 'decimal:2',
        'overseas_package_volume' => 'decimal:2',
    ];

    /**
     * 获取产品所属的分类。
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    /**
     * 获取该产品的所有销售订单项。
     */
    public function salesOrderItems(): HasMany
    {
        return $this->hasMany(SalesOrderItem::class);
    }

    /**
     * 获取该产品的所有采购订单项。
     */
    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * 获取产品图片的完整URL。
     *
     * @return string|null
     */
    public function getImageUrlAttribute(): ?string
    {
        $media = $this->getFirstMedia('default');
        return $media ? $media->getUrl() : null;
    }
    /**
     * 获取产品图片的完整路径。
     *
     * @return string|null
     */
    public function getImagePathAttribute(): ?string
    {
        $media = $this->getFirstMedia('default');
        return $media ? $media->getPath() : null;
    }
}
