<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id 主键ID
 * @property string $order_number 销售订单号
 * @property int $customer_id 客户ID
 * @property int|null $contact_id 联系人ID
 * @property int|null $opportunity_id 关联商机ID
 * @property string $order_date 订单日期
 * @property string|null $requested_delivery_date 交货日期
 * @property string|null $shipping_address 送货地址
 * @property string $original_amount 订单原价总额
 * @property string $discount_amount 优惠金额
 * @property string $total_amount 订单金额
 * @property string $total_amount_in_words 订单金额大写
 * @property string $status 状态
 * @property string|null $payment_method 付款方式
 * @property int|null $salesperson_id 销售员ID
 * @property string|null $attachments 附件组
 * @property string|null $images 图片组
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @property \Illuminate\Support\Carbon $updated_at 更新时间
 * @property string|null $deleted_at 删除时间 (软删除)
 * @property int $creator_id 创建人ID
 * @property int $updater_id 最后更新人ID
 * @property-read \App\Models\Contact|null $contact
 * @property-read \App\Models\Customer|null $customer
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SalesOrderItem> $items
 * @property-read int|null $items_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereAttachments($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereImages($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereOpportunityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereOrderDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereOrderNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereOriginalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereRequestedDeliveryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereSalespersonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereShippingAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereTotalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereTotalAmountInWords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SalesOrder whereUpdaterId($value)
 * @mixin \Eloquent
 */
class SalesOrder extends Model
{
    /**
     * 获取订单所属的客户。
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * 获取订单的联系人。
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * 获取订单的所有订单项。
     */
    public function items(): HasMany
    {
        return $this->hasMany(SalesOrderItem::class, 'sales_order_id');
    }
}
