<?php

namespace App\Exports;

use App\Enums\UnitEnum;
use App\Models\Quotation;
use App\Utils\TranslateUtil;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCharts;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class QuotationExport implements FromArray, WithStyles, WithEvents
{
    protected $quotation;
    protected float $exchangeRate = 1.0; // 默认汇率为1，即不转换

    public function __construct($quotationId)
    {
        $this->quotation = Quotation::query()->where('id', $quotationId)->with(['customer', 'contact', 'items.product'])->first();
    }

    // 可被子类重写的方法
    protected function getHeader(): array
    {
        return [
            ['江苏奥斯丁公司报价单', '', '', '', '', '', '', '', '', '', '', '', '', '']
        ];
    }

    protected function getDetailsHeader(): array
    {
        return [
            '序号', '产品图片', '名称', '规格', '单位', '数量', '单价', '总价(¥)', '单位重量(kg)', '总重量(kg)', '装箱数量', '箱数', '箱立方米', '总立方米', '备注'
        ];
    }

    protected function getQuotationNumberLabel(): string
    {
        return '报价单号';
    }

    protected function getQuotationDateLabel(): string
    {
        return '报价日期';
    }

    protected function getTotalLabel(): string
    {
        return '合计';
    }

    protected function getRemarkLabel(): string
    {
        return '备注';
    }

    protected function processText($text): string
    {
        return (string)$text;
    }

    protected function processUnit(string $unit): string
    {
        return $unit;
    }

    /**
     * 根据数值和单位生成箱装量信息
     *
     * @param int $boxPackagingInfo 装箱数量
     * @param string $unit 单位
     * @return string 格式化后的箱装量信息，如 "100 双/箱"
     */
    protected function formatBoxPackagingInfo(int $boxPackagingInfo, string $unit): string
    {
        // 如果装箱数量为0或负数，直接返回空字符串
        if ($boxPackagingInfo <= 0) {
            return '';
        }

        // 尝试匹配单位枚举
        foreach (UnitEnum::cases() as $unitEnum) {
            if ($unitEnum->value === $unit) {
                // 根据单位枚举确定显示单位
                $unitDisplay = match($unitEnum) {
                    UnitEnum::Piece => '个',
                    UnitEnum::Pair => '双',
                    UnitEnum::Set => '套',
                    UnitEnum::Box => '盒',
//                    UnitEnum::Carton => '箱',
                    default => $unitEnum->value
                };

                return $boxPackagingInfo . ' ' . $unitDisplay . '/箱';
            }
        }

        // 如果没有匹配到枚举，使用默认格式
        return $boxPackagingInfo . ' ' . $unit . '/箱';
    }

    protected function processPrice($price): float
    {
        return bcdiv($price, $this->exchangeRate, 2);
    }

    /**
     * @return array
     */
    public function array(): array
    {
        $header = $this->getHeader();
        // 基本信息
        $basicInfo = [
            ['',$this->getQuotationNumberLabel(), $this->quotation->quotation_number, $this->getQuotationDateLabel(), $this->quotation->quotation_date],
        ];

        // 报价详情表头
        $detailsHeader = $this->getDetailsHeader();
        // 报报详情数据
        $detailsData = [];

        // 计算合计值
        $totalBoxQty = 0;
        $totalVolume = 0;
        $totalPrice = 0;
        $totalWeight = 0;

        foreach ($this->quotation->items as $index=>$item) {
            $imageUrl = '';

            // 计算箱数（向上取整）
            $boxQty = 0;
            $itemTotalVolume = 0;
            $boxVolume = $item->product->overseas_package_volume ?? 0;
            $boxPackagingInfo = $item->product->overseas_package_info ?? 0;

            if ($boxPackagingInfo > 0) {
                $boxQty = ceil($item->quantity / $boxPackagingInfo);
                $itemTotalVolume = bcmul((string)$boxVolume, (string)$boxQty, 2);
            }

            // 计算总重量
            $unitWeight = $item->product->weight ?? 0;
            $itemTotalWeight = bcmul((string)$unitWeight, (string)$item->quantity, 2);

            // 累计合计值
            $totalBoxQty += $boxQty;
            $totalVolume = bcadd((string)$totalVolume, (string)$itemTotalVolume, 2);
            $totalPrice = bcadd((string)$totalPrice, (string)$this->processPrice($item->total_line_amount), 2);
            $totalWeight = bcadd((string)$totalWeight, (string)$itemTotalWeight, 2);

            $detailsData[] = [
                $index + 1,
                $imageUrl, // 产品图片移到序号右边
                $this->processText($item->name),
                $this->processText($item->spec),
                $this->processUnit($item->unit),
                $item->quantity,
                $this->processPrice($item->unit_price),
                $this->processPrice($item->total_line_amount),
                $unitWeight, // 单位重量
                $itemTotalWeight, // 总重量
                $this->formatBoxPackagingInfo($boxPackagingInfo, $item->unit), // 箱装量
                intval($boxQty), // 箱数
                bcdiv((string)$boxVolume, '1000', 2), // 箱立方米
                bcdiv((string)$itemTotalVolume, '1000', 2), // 总立方米
                $this->processText($item->remark), // 备注移到最后
            ];
        }

        // 金额信息
        $amountInfo = [
            [$this->getTotalLabel(), '', $this->getTotalLabel(), '', '', '', $totalPrice, '', $totalWeight, '', $totalBoxQty, '', bcdiv((string)$totalVolume, '1000', 2), '', ''],
            [$this->getRemarkLabel(), $this->processText($this->quotation->remark)]
        ];

        return [
            $header,
            ...$basicInfo,
            [],
            $detailsHeader,
            ...$detailsData,
            [],
            ...$amountInfo,
        ];
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $highestRow = $event->sheet->getDelegate()->getHighestRow();
                $highestColumn = $event->sheet->getDelegate()->getHighestColumn();
                // 合并标题行的单元格
                $event->sheet->getDelegate()->mergeCells('A1:O1');
                // 合并备注
                $event->sheet->getDelegate()->mergeCells("B{$highestRow}:{$highestColumn}{$highestRow}");
                $event->sheet->getDelegate()->getRowDimension("{$highestRow}")->setRowHeight(40);

                // B列和C列宽度设为20
                $event->sheet->getDelegate()->getColumnDimension('B')->setWidth(15);
                $event->sheet->getDelegate()->getColumnDimension('C')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('D')->setWidth(20);
                // N列和O列宽度设为15和20
                $event->sheet->getDelegate()->getColumnDimension('N')->setWidth(15);
                $event->sheet->getDelegate()->getColumnDimension('O')->setWidth(20);
                // F到 M 设置列宽度 15
                for ($col = 'F'; $col <= 'M'; $col++) {
                    $event->sheet->getDelegate()->getColumnDimension($col)->setWidth(15);
                }

                // 设置标题样式 - 字体大小20，居中对齐
                $styleTitle = $event->sheet->getDelegate()->getStyle('A1');
                $styleTitle->getFont()->setSize(20);
                $styleTitle->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

                // 设置表头加粗并添加灰色背景
                $event->sheet->getDelegate()->getStyle('A3:O3')->getFont()->setBold(true);
                $event->sheet->getDelegate()->getStyle('A3:O3')
                    ->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $event->sheet->getDelegate()->getStyle('A3:O3')
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setRGB('DCDCDC');

                // 为整个表格添加边框

                $range = 'A1:' . $highestColumn . $highestRow;

                $event->sheet->getDelegate()->getStyle($range)
                    ->getBorders()
                    ->getAllBorders()
                    ->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

                // 设置数据行垂直居中
                $dataStartRow = 4;
                $dataEndRow = 3 + count($this->quotation->items);
                for ($row = $dataStartRow; $row <= $dataEndRow; $row++) {
                    $event->sheet->getDelegate()->getStyle("A{$row}:O{$row}")
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                        ->setVertical(Alignment::VERTICAL_CENTER);
                }

                // 设置名称、规格、备注列 自动换行
                $event->sheet->getDelegate()->getStyle('C4:C' . ($dataEndRow))
                    ->getAlignment()
                    ->setWrapText(true);
                $event->sheet->getDelegate()->getStyle('D4:D' . ($dataEndRow))
                    ->getAlignment()
                    ->setWrapText(true);
                $event->sheet->getDelegate()->getStyle('O4:O' . ($dataEndRow))
                    ->getAlignment()
                    ->setWrapText(true);

                // 设置合计行加粗并添加灰色背景
                $totalRow = $dataEndRow + 1;
                $event->sheet->getDelegate()->getStyle("A{$totalRow}:O{$totalRow}")->getFont()->setBold(true);
                $event->sheet->getDelegate()->getStyle("A{$totalRow}:O{$totalRow}")
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setRGB('DCDCDC');

                // 处理图片插入和行高设置
                $rowIndex = 4; // 从第5行开始是数据行
                foreach ($this->quotation->items as $index => $item) {
                    // 设置行高与图片高度一致
                    $event->sheet->getDelegate()->getRowDimension($rowIndex + $index)->setRowHeight(100);

                    if ($item->product && $item->product->image_path) {
                        $imagePath = $item->product->image_path;
                        if (file_exists($imagePath)) {
                            $drawing = new Drawing();
                            $drawing->setName('Product Image');
                            $drawing->setDescription('Product Image');
                            $drawing->setPath($imagePath);
                            $drawing->setHeight(130); // 设置图片高度略小于行高
                            $drawing->setCoordinates('B' . ($rowIndex + $index)); // 修改为新的列位置
                            $drawing->setWorksheet($event->sheet->getDelegate());
                        }
                    }
                }
            }
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            // 可以在这里设置样式
        ];
    }
}
