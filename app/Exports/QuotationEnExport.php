<?php

namespace App\Exports;

use App\Enums\UnitEnum;
use App\Utils\TranslateUtil;

class QuotationEnExport extends QuotationExport
{
    protected float $exchangeRate = 7.1; // 默认汇率为1，即不转换
    protected function getHeader(): array
    {
        return [
            ['Jiangsu Austin Company Quotation', '', '', '', '', '', '', '', '', '', '', '', '', '']
        ];
    }

    protected function getDetailsHeader(): array
    {
        return [
            // 序号 产品图片 名称 规格 单位 数量 单价 总价(¥) 单位重量(kg) 总重量(kg) 箱装量 箱数 箱立方米 总立方米 备注
            'No.', 'Photo', 'Name', 'Spec', 'Unit', 'Quantity', 'Unit Price($)', 'Total Price($)', 'Unit Weight(kg)', 'Total Weight(kg)', 'Packing Info', 'Total CTNs', 'Carton Volume(m³)', 'Total Volume(m³)', 'Remark'
        ];
    }

    protected function getQuotationNumberLabel(): string
    {
        return 'Quotation No.';
    }

    protected function getQuotationDateLabel(): string
    {
        return 'Quotation Date';
    }

    protected function getTotalLabel(): string
    {
        return 'Total';
    }

    protected function getRemarkLabel(): string
    {
        return 'Remark';
    }

    protected function processText($text): string
    {
        return TranslateUtil::translateCache($text);
    }

    protected function processUnit(string $unit): string
    {

        // 如果传入的是字符串值，尝试查找匹配的枚举
        foreach (UnitEnum::cases() as $unitEnum) {
            if ($unitEnum->value === $unit) {
                return $unitEnum->name;
            }
        }
        // 如果没有找到匹配的枚举，返回原值
        return $unit;
    }

    /**
     * 根据数值和单位生成箱装量信息（英文版）
     *
     * @param int $boxPackagingInfo 装箱数量
     * @param string $unit 单位
     * @return string 格式化后的箱装量信息，如 "100 Pair/Carton"
     */
    protected function formatBoxPackagingInfo(int $boxPackagingInfo, string $unit): string
    {
        // 如果装箱数量为0或负数，直接返回空字符串
        if ($boxPackagingInfo <= 0) {
            return '';
        }

        // 尝试匹配单位枚举
        foreach (UnitEnum::cases() as $unitEnum) {
            if ($unitEnum->value === $unit) {
                // 根据单位枚举确定显示单位
                $unitDisplay = match($unitEnum) {
                    UnitEnum::Piece => 'PCS',
                    UnitEnum::Pair => 'Pair',
                    UnitEnum::Set => 'Set',
                    UnitEnum::Box => 'Box',
//                    UnitEnum::Carton => 'Carton',
                    default => $unitEnum->name
                };

                return $boxPackagingInfo . ' ' . $unitDisplay . '/Carton';
            }
        }

        // 如果没有匹配到枚举，使用默认格式
        return $boxPackagingInfo . ' ' . $unit . '/Carton';
    }
}