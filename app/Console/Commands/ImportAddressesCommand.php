<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class ImportAddressesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-addresses {--file=pca.json : JSON文件路径}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从 pca.json 文件导入地址数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $file = $this->option('file');
        $filePath = resource_path($file);
        
        if (!file_exists($filePath)) {
            $this->error("文件不存在: {$filePath}");
            return Command::FAILURE;
        }

        $jsonContent = file_get_contents($filePath);
        $data = json_decode($jsonContent, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('JSON 文件格式错误: ' . json_last_error_msg());
            return Command::FAILURE;
        }

        // 清空现有数据
        \App\Models\Address::truncate();
        $this->info('已清空现有地址数据');

        $addressesToInsert = [];
        $this->processAddressData($data, '', '', 1, $addressesToInsert);
        
        $this->info('准备插入 ' . count($addressesToInsert) . ' 条记录');

        // 批量插入数据
        $chunks = array_chunk($addressesToInsert, 1000);
        $progressBar = $this->output->createProgressBar(count($chunks));
        $progressBar->start();

        DB::transaction(function () use ($chunks, $progressBar) {
            foreach ($chunks as $chunk) {
                \App\Models\Address::insert($chunk);
                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info('地址数据导入完成');

        return Command::SUCCESS;
    }

    /**
     * 递归处理地址数据
     *
     * @param array $data 地址数据数组
     * @param string $parentName 父级名称
     * @param string $provinceName 省份名称
     * @param int $level 级别 1省 2市 3区
     * @param array &$addressesToInsert 要插入的地址数据引用
     */
    private function processAddressData(array $data, string $parentName, string $provinceName, int $level, array &$addressesToInsert): void
    {
        foreach ($data as $key => $value) {
            // 如果当前值是数组，则表示这是一个父节点
            if (is_array($value)) {
                // 获取当前节点的名称
                $currentName = $key;
                
                // 如果当前名称是"市辖区"，则使用省份名称作为名称
                $actualName = ($currentName === '市辖区') ? $provinceName : $currentName;
                
                $addressesToInsert[] = [
                    'parent_name' => $parentName,
                    'name' => $actualName,
                    'level' => $level,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // 如果是省级节点，则更新省份名称
                $currentProvinceName = (empty($parentName)) ? $actualName : $provinceName;

                // 递归处理子节点，级别+1
                $this->processAddressData($value, $actualName, $currentProvinceName, $level + 1, $addressesToInsert);
            } else {
                // 如果当前值不是数组，则表示这是一个叶子节点
                $actualName = ($value === '市辖区') ? $provinceName : $value;
                
                $addressesToInsert[] = [
                    'parent_name' => $parentName,
                    'name' => $actualName,
                    'level' => $level,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
    }
}