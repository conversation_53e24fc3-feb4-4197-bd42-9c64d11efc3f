<?php

namespace App\Http\Controllers;

use App\Exports\QuotationExport;
use App\Models\Quotation;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class TestController extends Controller
{
    public function export()
    {
        // 获取第一个报价单记录进行测试
        $quotation = Quotation::first();

        if (!$quotation) {
            return response()->json(['error' => '没有找到报价单记录'], 404);
        }

        // 创建导出实例
        $export = new QuotationExport($quotation->id);

        return Excel::download($export, 'users.xlsx');
    }
}
