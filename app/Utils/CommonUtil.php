<?php

namespace App\Utils;

class CommonUtil
{

    /**
     * 将数字金额转换为中文大写
     *
     * @param float $amount
     * @return string
     */
    public static function convertAmountToChinese(string|float $amount): string
    {
        $amount = round($amount, 2);

        // 中文大写数字
        $digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];

        // 中文金额单位
        $units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟'];

        // 小数部分单位
        $decimalUnits = ['角', '分'];

        $result = '';

        // 处理整数部分
        $integerPart = (int)$amount;
        if ($integerPart == 0) {
            $result .= '零';
        } else {
            $integerStr = strrev($integerPart); // 反转字符串便于处理
            $integerResult = '';

            for ($i = 0; $i < strlen($integerStr); $i++) {
                $digit = (int)$integerStr[$i];
                $unit = $units[$i];
                $integerResult = $digits[$digit] . $unit . $integerResult;

                // 处理零的情况
                if ($digit == 0) {
                    // 如果当前位是0，并且不是最后一位
                    if ($i > 0 && $i < strlen($integerStr) - 1) {
                        // 检查是否需要添加零
                        $nextDigit = (int)($integerStr[$i + 1] ?? 0);
                        if ($nextDigit != 0) {
                            $integerResult = $digits[0] . $integerResult;
                        }
                    }
                }
            }

            // 处理连续零的情况
            $integerResult = preg_replace('/零{2,}/', '零', $integerResult);
            $integerResult = rtrim($integerResult, '零');

            $result .= $integerResult;
        }

        // 添加元字
        $result .= '元';

        // 处理小数部分
        $decimalPart = round(($amount - $integerPart) * 100);
        if ($decimalPart > 0) {
            $jiao = floor($decimalPart / 10); // 角
            $fen = $decimalPart % 10; // 分

            if ($jiao > 0) {
                $result .= $digits[$jiao] . '角';
            }

            if ($fen > 0) {
                $result .= $digits[$fen] . '分';
            }
        } else {
            $result .= '整';
        }

        return $result;
    }

    /**
     * 数字转换成金额
     */
    public static function formatToAmount($amount): string
    {
        if (empty($amount)) {
            return '0.00';
        }
        return number_format($amount, 2, '.', '');
    }
    // 生成订单号
    public static function generateOrderNumber($prefix): string
    {
        $key = 'order_number_counter:' . $prefix . date('Ymd');
        $counter = \Cache::increment($key);
        if ($counter == 1) {
            \Cache::put($key, 1, now()->endOfDay());
        }
//        return $prefix . date('Ymd') . '-' . str_pad($counter, 3, '0', STR_PAD_LEFT);
        return $prefix . date('Ymd') . '-' . date('His');
    }
}
