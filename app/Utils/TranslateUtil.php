<?php

namespace App\Utils;

class TranslateUtil
{
    protected static $translator = null;

    public static function translate($text): string
    {
        if (empty($text)){
            return '';
        }
        $app = self::getApp();

        $translate = $app->translate($text);
        return $translate->getDst();
    }

    // translateCache
    public static function translateCache($text): string
    {
        $key = 'translate:' . $text;
        $result = \Cache::remember($key, 3600, function () use ($text) {
            return self::translate($text) ?: null;
        });
        return (string)$result;
    }

    /**
     */
    private static function getApp(): \Plugins\Translate\Translator\Youdao
    {
        if (is_null(static::$translator)) {
            static::$translator = new \Plugins\Translate\Translator\Youdao([
                'app_id' => '30ed61fe56cab738',
                'app_key' => 'SXUNVfUgy0F1Nb9KWCgRmp7C4seD7nVo',
            ]);
        }
        return static::$translator;
    }
}
