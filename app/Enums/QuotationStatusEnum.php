<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum QuotationStatusEnum: string implements HasLabel
{
    case DRAFT = 'draft';
    case SENT = 'sent';
    case ACCEPTED = 'accepted';
    case REJECTED = 'rejected';
    case EXPIRED = 'expired';
    case CONVERTED = 'converted';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::DRAFT => '草稿',
            self::SENT => '已发送',
            self::ACCEPTED => '已接受',
            self::REJECTED => '已拒绝',
            self::EXPIRED => '已过期',
            self::CONVERTED => '已转订单',
        };
    }
}
