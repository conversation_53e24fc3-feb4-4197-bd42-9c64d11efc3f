<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum CustomerSourceEnum: string implements HasLabel
{
    // 拼多多 阿里巴巴 阿里巴巴国际版 淘宝 抖音 小红书 快手 线下
    case Pdd = '拼多多';
    case Albb = '阿里巴巴';
    case AlbbGlobal = '阿里国际站';
    case Tb = '淘宝';
    case Dy = '抖音';
    case Xhs = '小红书';
    case Ks = '快手';
    case Offline = '线下';
    case Other = '其他';
    public function getLabel(): ?string
    {
        return match ($this) {
            self::Pdd => '拼多多',
            self::Albb => '阿里巴巴',
            self::AlbbGlobal => '阿里国际站',
            self::Tb => '淘宝',
            self::Dy => '抖音',
            self::Xhs => '小红书',
            self::Ks => '快手',
            self::Offline => '线下',
            self::Other => '其他',
        };
    }
}
