version: '3.8'

services:
  # PHP服务
  php:
    image: registry.cn-zhangjiakou.aliyuncs.com/wuzei/php:8.2-fpm-node22
    container_name: osderp-php
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - osderp

  # Nginx服务
  nginx:
    image: nginx:alpine
    container_name: osderp-nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker-compose/nginx:/etc/nginx/conf.d
    networks:
      - osderp
    depends_on:
      - php

# Docker网络
networks:
  osderp:
    driver: bridge
