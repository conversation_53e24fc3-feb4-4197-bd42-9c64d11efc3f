-- 产品分类表 (Product Categories)

CREATE TABLE `product_categories`
(

    `id`         bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `name`       varchar(100) NOT NULL COMMENT '分类名称',

    `parent_id`  bigint                DEFAULT NULL COMMENT '父分类ID (用于层级结构)',

    `created_at` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at` datetime              DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id` int                   DEFAULT 0 COMMENT '创建人ID',

    `updater_id` int                   DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    KEY `idx_parent_id` (`parent_id`)

) ENGINE = InnoDB COMMENT ='产品分类表';


-- 产品表 (Products)

CREATE TABLE `products`
(

    `id`                  bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `product_category_id` bigint                DEFAULT NULL COMMENT '产品分类ID',

    `image_url`             varchar(255) NOT NULL COMMENT '产品图片',

    `name`                varchar(100) NOT NULL COMMENT '产品名称',

    `spec`                varchar(100)          DEFAULT NULL COMMENT '规格',

    `desc`                varchar(255) COMMENT '产品描述',

    `unit`                varchar(6)            DEFAULT NULL COMMENT '单位 (如: 个, 件, kg)',

    `purchase_price`      decimal(10, 2)        DEFAULT '0.00' COMMENT '采购单价',

    `selling_price`       decimal(10, 2)        DEFAULT '0.00' COMMENT '销售单价',

    `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`          datetime              DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`          int                   DEFAULT 0 COMMENT '创建人ID',

    `updater_id`          int                   DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    KEY `idx_product_category_id` (`product_category_id`)

) ENGINE = InnoDB COMMENT ='产品表';
