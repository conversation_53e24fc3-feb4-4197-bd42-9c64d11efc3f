-- 销售订单主表 (Sales Orders - Header)

CREATE TABLE `sales_orders`
(

    `id`                      bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `order_number`            varchar(50) NOT NULL COMMENT '销售订单号',

    `customer_id`             bigint      NOT NULL COMMENT '客户ID',

    `contact_id`              bigint               DEFAULT 0 COMMENT '联系人ID',

    `opportunity_id`          bigint               DEFAULT 0 COMMENT '关联商机ID',

    `order_date`              date        NOT NULL COMMENT '订单日期',

    `requested_delivery_date` date                 DEFAULT NULL COMMENT '交货日期',

    `shipping_address`        varchar(255) COMMENT '送货地址',

    `original_amount`         decimal(12, 2)       DEFAULT '0.00' COMMENT '订单原价总额',
    `discount_amount`         decimal(12, 2)       DEFAULT '0.00' COMMENT '优惠金额',
    `total_amount`            decimal(12, 2)       DEFAULT '0.00' COMMENT '订单金额',
    `total_amount_in_words`   varchar(255)         NOT NULL COMMENT '订单金额大写',

    `status`                  varchar(20)          DEFAULT 'pending' COMMENT '状态', -- (如: pending, confirmed, processing, partially_shipped, shipped, invoiced, cancelled)

    `payment_method`          varchar(50)          DEFAULT NULL COMMENT '付款方式',

    `salesperson_id`          int                  DEFAULT NULL COMMENT '销售员ID',

    `attachments`             json COMMENT '附件组',
    `images`                  json COMMENT '图片组',
    `remark`                  text COMMENT '备注',

    `created_at`              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`              datetime             DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`              int                  DEFAULT 0 COMMENT '创建人ID',

    `updater_id`              int                  DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    UNIQUE KEY `uk_order_number` (`order_number`),

    KEY `idx_customer_id` (`customer_id`),

    KEY `idx_contact_id` (`contact_id`),

    KEY `idx_opportunity_id` (`opportunity_id`),

    KEY `idx_salesperson_id` (`salesperson_id`),

    KEY `idx_status` (`status`)

) ENGINE = InnoDB COMMENT ='销售订单主表';


-- 销售订单明细表 (Sales Orders - Items)

CREATE TABLE `sales_order_items`
(

    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `sales_order_id`    bigint         NOT NULL COMMENT '销售订单主表ID',

    `product_id`        bigint         NOT NULL COMMENT '销售产品ID',

    `quantity`          bigint         NOT NULL COMMENT '销售数量',

    `unit_price`        decimal(10, 2) NOT NULL COMMENT '单价',

    `discount_rate`     decimal(5, 4)           DEFAULT '0.0000' COMMENT '折扣率 (%)',

    `tax_rate`          decimal(5, 4)           DEFAULT '0.0000' COMMENT '税率 (%)',

    `total_line_amount` decimal(12, 2)          DEFAULT '0.00' COMMENT '行总价',

    `shipped_quantity`  decimal(10, 2)          DEFAULT '0.00' COMMENT '已发货数量',

    `unit`              varchar(20)             DEFAULT NULL COMMENT '单位',

    `remark`            varchar(255)            DEFAULT NULL COMMENT '备注',

    `created_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`        datetime                DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`        int                     DEFAULT 0 COMMENT '创建人ID',

    `updater_id`        int                     DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    KEY `idx_so_id` (`sales_order_id`),

    KEY `idx_product_id` (`product_id`)

) ENGINE = InnoDB COMMENT ='销售订单明细表';

-- 采购订单主表 (Purchase Orders - Header)

CREATE TABLE `purchase_orders`
(

    `id`                     bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `order_number`              varchar(50) NOT NULL COMMENT '采购订单号',

    `supplier_id`            bigint      NOT NULL COMMENT '供应商ID',

    `order_date`             date        NOT NULL COMMENT '订单日期',

    `expected_delivery_date` date                 DEFAULT NULL COMMENT '预计到货日期',

    `shipping_address`       text COMMENT '收货地址',

    `original_amount`         decimal(12, 2)       DEFAULT '0.00' COMMENT '订单原价总额',
    `discount_amount`         decimal(12, 2)       DEFAULT '0.00' COMMENT '优惠金额',
    `total_amount`            decimal(12, 2)       DEFAULT '0.00' COMMENT '订单金额',
    `total_amount_in_words`   varchar(255)         NOT NULL COMMENT '订单金额大写',

    `status`                 varchar(20)          DEFAULT 'pending' COMMENT '状态' , -- (如: pending, approved, partially_received, received, cancelled)

    `payment_terms`          varchar(100)         DEFAULT NULL COMMENT '付款条件',

    `remark`                 text COMMENT '备注',

    `created_at`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`             datetime             DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`             int                  DEFAULT 0 COMMENT '创建人ID',

    `updater_id`             int                  DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    UNIQUE KEY `uk_po_number` (`order_number`),

    KEY `idx_supplier_id` (`supplier_id`),

    KEY `idx_status` (`status`)

) ENGINE = InnoDB COMMENT ='采购订单主表';


-- 采购订单明细表 (Purchase Orders - Items)

CREATE TABLE `purchase_order_items`
(

    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `purchase_order_id` bigint         NOT NULL COMMENT '采购订单主表ID',

    `product_id`        bigint         NOT NULL COMMENT '采购产品ID',

    `quantity`          decimal(10, 2) NOT NULL COMMENT '采购数量',

    `unit_price`        decimal(10, 2) NOT NULL COMMENT '单价',

    `total_line_amount` decimal(12, 2)          DEFAULT '0.00' COMMENT '行总价', -- (需应用计算: quantity * unit_price)

    `received_quantity` decimal(10, 2)          DEFAULT '0.00' COMMENT '已接收数量',

    `unit`              varchar(20)             DEFAULT NULL COMMENT '单位',

    `remark`            varchar(255)            DEFAULT NULL COMMENT '备注',

    `created_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`        datetime                DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`        int                     DEFAULT 0 COMMENT '创建人ID',

    `updater_id`        int                     DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    KEY `idx_po_id` (`purchase_order_id`),

    KEY `idx_product_id` (`product_id`)

) ENGINE = InnoDB COMMENT ='采购订单明细表';
