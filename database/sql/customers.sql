-- 客户表 (Customers)

CREATE TABLE `customers`
(

    `id`                  bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `name`                varchar(255) NOT NULL COMMENT '客户名称',

    `source`              varchar(50)           DEFAULT NULL COMMENT '客户来源',

    `telephone`           varchar(50)           DEFAULT NULL COMMENT '公司电话',

    `website`             varchar(255)          DEFAULT NULL COMMENT '公司网址',

    `province`            varchar(100)          DEFAULT NULL COMMENT '省',
    `city`                varchar(100)          DEFAULT NULL COMMENT '市',
    `district`            varchar(100)          DEFAULT NULL COMMENT '区',
    `address`             varchar(255)          DEFAULT NULL COMMENT '详细地址',
    `full_address`        varchar(255)          DEFAULT NULL COMMENT '完整地址',

    `assigned_to_user_id` int                   DEFAULT NULL COMMENT '负责人ID',

    `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`          datetime              DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`          int                   DEFAULT 0 COMMENT '创建人ID',

    `updater_id`          int                   DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),


    KEY `idx_assigned_to` (`assigned_to_user_id`)

) ENGINE = InnoDB COMMENT ='客户表';


-- 联系人表 (Contacts)

CREATE TABLE `contacts`
(

    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    `customer_id`  bigint       NOT NULL COMMENT '所属客户ID',

    `name`         varchar(100) NOT NULL COMMENT '联系人姓名',

    `title`        varchar(100)          DEFAULT NULL COMMENT '职位',

    `department`   varchar(100)          DEFAULT NULL COMMENT '部门',

    `email`        varchar(100)          DEFAULT NULL COMMENT '邮箱',

    `mobile_phone` varchar(50)           DEFAULT NULL COMMENT '手机',

    `telephone` varchar(50)           DEFAULT NULL COMMENT '办公电话',

    `is_primary`   tinyint(1)            DEFAULT '0' COMMENT '是否主要联系人',

    `created_at`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    `updated_at`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    `deleted_at`   datetime              DEFAULT NULL COMMENT '删除时间 (软删除)',

    `creator_id`   int                   DEFAULT 0 COMMENT '创建人ID',

    `updater_id`   int                   DEFAULT 0 COMMENT '最后更新人ID',

    PRIMARY KEY (`id`),

    KEY `idx_customer_id` (`customer_id`),

    KEY `idx_email` (`email`),

    KEY `idx_mobile_phone` (`mobile_phone`)

) ENGINE = InnoDB COMMENT ='联系人表';
