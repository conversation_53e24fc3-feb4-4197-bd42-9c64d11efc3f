<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('size', 30)->nullable()->comment('规格尺寸 如:1*2*3cm');
            $table->decimal('weight', 10, 2)->nullable()->comment('重量');
            $table->decimal('volume', 10, 2)->nullable()->comment('体积(cm³)');
            $table->string('package_info', 20)->nullable()->comment('包装规格 如:100双/箱');
            $table->string('package_size', 30)->nullable()->comment('包装尺寸 如:1*2*3cm');
            $table->decimal('package_volume', 10, 2)->nullable()->comment('包装体积(cm³)');
            $table->decimal('package_weight', 10, 2)->nullable()->comment('包装重量(kg)');
            $table->string('overseas_package_info', 20)->nullable()->comment('境外包装规格 如:100双/箱');
            $table->string('overseas_package_size', 30)->nullable()->comment('境外包装尺寸 如:1*2*3cm');
            $table->decimal('overseas_package_volume', 10, 2)->nullable()->comment('境外包装体积(cm³)');
            $table->decimal('overseas_package_weight', 10, 2)->nullable()->comment('境外包装重量(kg)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'weight',
                'size',
                'volume',
                'package_info',
                'package_size',
                'package_volume',
                'overseas_package_info',
                'overseas_package_size',
                'overseas_package_volume'
            ]);
        });
    }
};
