<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            $table->string('code', 20)->comment('行政区划代码');
            $table->string('parent_code', 20)->comment('父级代码');
            $table->string('name', 40)->comment('名称');
            $table->timestamps();

            $table->index('code', 'idx_code');
            $table->index('parent_code', 'idx_parent_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
