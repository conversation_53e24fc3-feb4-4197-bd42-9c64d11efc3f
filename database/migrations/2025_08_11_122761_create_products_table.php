<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_category_id')->comment('产品分类ID');
            $table->string('image_url', 255)->nullable()->comment('产品图片');
            $table->string('name', 100)->comment('产品名称');
            $table->string('spec', 100)->nullable()->comment('规格');
            $table->string('desc', 255)->nullable()->comment('产品描述');
            $table->string('unit', 6)->comment('单位 (如: 个, 件, kg)');
            $table->decimal('purchase_price', 10, 2)->default(0.00)->comment('采购单价');
            $table->decimal('selling_price', 10, 2)->default(0.00)->comment('销售单价');
            $table->decimal('overseas_selling_price', 10, 2)->default(0.00)->comment('境外销售单价');

            $table->string('size', 30)->nullable()->comment('规格尺寸 如:1*2*3cm');
            $table->decimal('weight', 10, 2)->default(0)->comment('重量(g)');
            $table->decimal('volume', 10, 2)->default(0)->comment('体积(cm³)');
            $table->string('package_info', 20)->nullable()->comment('包装规格 如:100双/箱');
            $table->string('package_size', 30)->nullable()->comment('包装尺寸 如:1*2*3cm');
            $table->decimal('package_volume', 10, 2)->default(0)->comment('包装体积(cm³)');
            $table->decimal('package_weight', 10, 2)->default(0)->comment('包装重量(kg)');
            $table->string('overseas_package_info', 20)->nullable()->comment('境外包装规格 如:100双/箱');
            $table->string('overseas_package_size', 30)->nullable()->comment('境外包装尺寸 如:1*2*3cm');
            $table->decimal('overseas_package_volume', 10, 2)->default(0)->comment('境外包装体积(cm³)');
            $table->decimal('overseas_package_weight', 10, 2)->default(0)->comment('境外包装重量(kg)');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->index('product_category_id', 'idx_product_category_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
