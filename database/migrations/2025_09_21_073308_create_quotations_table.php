<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('quotation_number', 50)->comment('报价单号');
            $table->unsignedBigInteger('customer_id')->comment('客户ID');
            $table->unsignedBigInteger('contact_id')->nullable()->comment('联系人ID');
            $table->date('quotation_date')->comment('报价日期');
            $table->date('expiry_date')->nullable()->comment('报价有效期');
            $table->string('shipping_address', 255)->nullable()->comment('送货地址');
            $table->decimal('original_amount', 12, 2)->default('0.00')->comment('报价原价总额');
            $table->decimal('discount_amount', 12, 2)->default('0.00')->comment('优惠金额');
            $table->decimal('total_amount', 12, 2)->default('0.00')->comment('报价金额');
            $table->string('total_amount_in_words', 255)->comment('报价金额大写');
            $table->string('status', 20)->default('pending')->comment('状态');
            $table->unsignedInteger('salesperson_id')->default(0)->comment('销售员ID');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamp('created_at')->comment('创建时间');
            $table->timestamp('updated_at')->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->unique('quotation_number', 'uk_quotation_number');
            $table->index('customer_id', 'idx_customer_id');
            $table->index('contact_id', 'idx_contact_id');
            $table->index('salesperson_id', 'idx_salesperson_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};
