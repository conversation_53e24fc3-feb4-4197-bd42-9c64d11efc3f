<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_order_items', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('sales_order_id')->comment('销售订单主表ID');
            $table->unsignedBigInteger('product_id')->comment('销售产品ID');
            $table->bigInteger('quantity')->comment('销售数量');
            $table->decimal('unit_price', 10, 2)->comment('单价');
            $table->decimal('discount_rate', 5, 4)->default('0.0000')->comment('折扣率 (%)');
            $table->decimal('tax_rate', 5, 4)->default('0.0000')->comment('税率 (%)');
            $table->decimal('total_line_amount', 12, 2)->default('0.00')->comment('行总价');
            $table->decimal('shipped_quantity', 10, 2)->default('0.00')->comment('已发货数量');
            $table->string('unit', 20)->nullable()->comment('单位');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->index('sales_order_id', 'idx_so_id');
            $table->index('product_id', 'idx_product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_order_items');
    }
};