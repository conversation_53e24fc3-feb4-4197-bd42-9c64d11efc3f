<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('parent_name', 40)->after('id')->comment('父级名称')->nullable();
            $table->dropColumn(['code', 'parent_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('code', 20)->comment('行政区划代码');
            $table->string('parent_code', 20)->comment('父级代码');
            $table->dropColumn('parent_name');
        });
    }
};