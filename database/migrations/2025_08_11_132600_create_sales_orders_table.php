<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_orders', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('order_number', 50)->comment('销售订单号');
            $table->unsignedBigInteger('customer_id')->comment('客户ID');
            $table->unsignedBigInteger('contact_id')->nullable()->comment('联系人ID');
            $table->unsignedBigInteger('opportunity_id')->nullable()->comment('关联商机ID');
            $table->date('order_date')->comment('订单日期');
            $table->date('requested_delivery_date')->nullable()->comment('交货日期');
            $table->string('shipping_address', 255)->nullable()->comment('送货地址');
            $table->decimal('original_amount', 12, 2)->default('0.00')->comment('订单原价总额');
            $table->decimal('discount_amount', 12, 2)->default('0.00')->comment('优惠金额');
            $table->decimal('total_amount', 12, 2)->default('0.00')->comment('订单金额');
            $table->string('total_amount_in_words', 255)->comment('订单金额大写');
            $table->string('status', 20)->default('pending')->comment('状态');
            $table->string('payment_method', 50)->nullable()->comment('付款方式');
            $table->unsignedInteger('salesperson_id')->nullable()->comment('销售员ID');
            $table->json('attachments')->nullable()->comment('附件组');
            $table->json('images')->nullable()->comment('图片组');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->unique('order_number', 'uk_order_number');
            $table->index('customer_id', 'idx_customer_id');
            $table->index('contact_id', 'idx_contact_id');
            $table->index('opportunity_id', 'idx_opportunity_id');
            $table->index('salesperson_id', 'idx_salesperson_id');
            $table->index('status', 'idx_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_orders');
    }
};