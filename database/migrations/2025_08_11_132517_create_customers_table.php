<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 255)->comment('客户名称');
            $table->string('source', 50)->nullable()->comment('客户来源');
            $table->string('telephone', 50)->nullable()->comment('公司电话');
            $table->string('website', 255)->nullable()->comment('公司网址');
            $table->string('province', 100)->nullable()->comment('省');
            $table->string('city', 100)->nullable()->comment('市');
            $table->string('district', 100)->nullable()->comment('区');
            $table->string('address', 255)->nullable()->comment('详细地址');
            $table->string('full_address', 255)->nullable()->comment('完整地址');
            $table->unsignedBigInteger('assigned_to_user_id')->nullable()->comment('负责人ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->index('assigned_to_user_id', 'idx_assigned_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};