<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('order_number', 50)->comment('采购订单号');
            $table->unsignedBigInteger('supplier_id')->comment('供应商ID');
            $table->date('order_date')->comment('订单日期');
            $table->date('expected_delivery_date')->nullable()->comment('预计到货日期');
            $table->text('shipping_address')->nullable()->comment('收货地址');
            $table->decimal('original_amount', 12, 2)->default('0.00')->comment('订单原价总额');
            $table->decimal('discount_amount', 12, 2)->default('0.00')->comment('优惠金额');
            $table->decimal('total_amount', 12, 2)->default('0.00')->comment('订单金额');
            $table->string('total_amount_in_words', 255)->comment('订单金额大写');
            $table->string('status', 20)->default('pending')->comment('状态');
            $table->string('payment_terms', 100)->nullable()->comment('付款条件');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->unique('order_number', 'uk_po_number');
            $table->index('supplier_id', 'idx_supplier_id');
            $table->index('status', 'idx_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};