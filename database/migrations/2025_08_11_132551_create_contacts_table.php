<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('customer_id')->comment('所属客户ID');
            $table->string('name', 100)->comment('联系人姓名');
            $table->string('title', 100)->nullable()->comment('职位');
            $table->string('department', 100)->nullable()->comment('部门');
            $table->string('email', 100)->nullable()->comment('邮箱');
            $table->string('mobile_phone', 50)->nullable()->comment('手机');
            $table->string('telephone', 50)->nullable()->comment('办公电话');
            $table->boolean('is_primary')->default(false)->comment('是否主要联系人');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamp('deleted_at')->nullable()->comment('删除时间 (软删除)');
            $table->integer('creator_id')->default(0)->comment('创建人ID');
            $table->integer('updater_id')->default(0)->comment('最后更新人ID');

            $table->index('customer_id', 'idx_customer_id');
            $table->index('email', 'idx_email');
            $table->index('mobile_phone', 'idx_mobile_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};