<?php

namespace Tests\Unit;

use App\Utils\TranslateUtil;
use PHPUnit\Framework\TestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic test example.
     */
    public function test_that_true_is_true(): void
    {
        $this->assertTrue(true);
    }
    // test plugins-world/translate
    public function test_translate(): void
    {
         $app = new \Plugins\Translate\Translator\Youdao([
             'app_id' => '30ed61fe56cab738',
             'app_key' => 'SXUNVfUgy0F1Nb9KWCgRmp7C4seD7nVo',
         ]);
        $translate = $app->translate('你好');
        $this->assertEquals('Hello', $translate->getDst());
        $this->assertEquals('Hello', TranslateUtil::translateCache('你好'));
    }
}
